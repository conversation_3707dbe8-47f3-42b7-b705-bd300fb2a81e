# MaixCAM Pro 步进电机驱动库

这是一个适用于MaixCAM Pro的步进电机驱动库，基于原始树莓派PICO代码移植而来。

## 文件说明

- `motor_driver.py` - 主要的驱动库文件
- `motor_example.py` - 使用示例代码
- `motor_contral.py` - 原始的树莓派PICO代码（参考用）

## 功能特性

- 支持双电机控制
- 串口通信控制
- 速度控制
- 位置控制
- 实时位置读取
- 回零功能
- 多机同步运动
- LED状态指示

## 快速开始

### 1. 导入驱动库

```python
from motor_driver import MotorDriver
```

### 2. 创建驱动实例

```python
# 使用默认参数
motor = MotorDriver()

# 或者自定义参数
motor = MotorDriver(
    uart1_id=1,      # UART1端口
    uart2_id=0,      # UART2端口  
    baudrate=115200, # 波特率
    uart1_tx=4,      # UART1发送引脚
    uart1_rx=5,      # UART1接收引脚
    uart2_tx=12,     # UART2发送引脚
    uart2_rx=13,     # UART2接收引脚
    led_pin=25       # LED指示灯引脚
)
```

### 3. 基本使用

```python
import time

# 使能电机
motor.enable_motor(1, True, False)  # 使能电机1

# 速度控制
motor.velocity_control(1, 0, 300, 30, False)  # 电机1，300RPM
time.sleep(2)

# 停止电机
motor.stop_motor(1, False)

# 位置控制
motor.position_control(1, 0, 200, 20, 1000, True, False)  # 相对运动1000脉冲

# 获取实时位置
pos1, pos2 = motor.get_real_time_position()
print(f"电机1位置: {pos1:.1f}°, 电机2位置: {pos2:.1f}°")

# 禁用电机
motor.enable_motor(1, False, False)
```

## API 参考

### 基本控制

- `enable_motor(addr, state, sync_flag)` - 电机使能控制
- `velocity_control(addr, direction, velocity, acceleration, sync_flag)` - 速度控制
- `position_control(addr, direction, velocity, acceleration, pulses, relative_flag, sync_flag)` - 位置控制
- `stop_motor(addr, sync_flag)` - 停止电机

### 位置和状态

- `get_real_time_position()` - 获取实时位置
- `reset_current_position(addr)` - 位置清零
- `read_sys_params(addr, param_type)` - 读取系统参数

### 回零功能

- `trigger_origin_return(addr, o_mode, sync_flag)` - 触发回零
- `set_origin_zero(addr, save_flag)` - 设置零点位置
- `interrupt_origin(addr)` - 中断回零

### 同步控制

- `synchronous_motion(addr)` - 执行同步运动

### 其他功能

- `set_led(state)` - 设置LED状态
- `print_position()` - 打印位置信息

## 参数说明

### 电机地址 (addr)
- 1: 电机1
- 2: 电机2

### 方向 (direction)
- 0: 顺时针 (CW)
- 1: 逆时针 (CCW)

### 同步标志 (sync_flag)
- True: 启用多机同步
- False: 不启用同步

### 相对/绝对标志 (relative_flag)
- True: 相对运动
- False: 绝对运动

## 注意事项

1. 请根据MaixCAM Pro的实际引脚配置调整UART引脚参数
2. 确保电机驱动板的波特率设置为115200
3. 使用前请确保电机驱动板正确连接
4. 建议在程序结束时禁用电机以节省功耗

## 示例代码

查看 `motor_example.py` 文件获取完整的使用示例，包括：

- 基本电机控制
- 位置监控
- 回零操作
- 多机同步运动

## 故障排除

1. **电机不响应**: 检查串口连接和波特率设置
2. **位置读取错误**: 确保电机驱动板固件版本兼容
3. **LED不亮**: 检查LED引脚配置是否正确

## 移植说明

本驱动库从树莓派PICO代码移植而来，主要改动：

1. 将原始函数封装为类方法
2. 优化了参数传递方式
3. 添加了更好的错误处理
4. 提供了更友好的API接口
5. 适配了MaixCAM Pro的硬件特性
